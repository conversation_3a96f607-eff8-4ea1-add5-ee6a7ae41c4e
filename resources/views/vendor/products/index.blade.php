@extends('layouts.admin')

@section('content')
<div x-data="vendorProducts()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Products</h1>
        <a href="{{ route('vendor.products.create') }}" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
            <i class="fas fa-plus mr-2"></i> Add Product
        </a>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <a href="{{ route('vendor.products.create') }}" class="bg-white p-6 rounded-lg shadow-lg text-center hover:shadow-xl transition-shadow">
            <i class="fas fa-plus-circle text-3xl text-green-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Add New Product</h3>
            <p class="text-gray-600 text-sm">Create a new product listing</p>
        </a>
        <a href="#" class="bg-white p-6 rounded-lg shadow-lg text-center hover:shadow-xl transition-shadow">
            <i class="fas fa-tags text-3xl text-blue-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Manage Categories</h3>
            <p class="text-gray-600 text-sm">Organize your products</p>
        </a>
        <a href="#" class="bg-white p-6 rounded-lg shadow-lg text-center hover:shadow-xl transition-shadow">
            <i class="fas fa-file-import text-3xl text-purple-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Import Products</h3>
            <p class="text-gray-600 text-sm">Bulk upload from a CSV file</p>
        </a>
    </div>

    <!-- Products Table -->
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex justify-between items-center mb-4">
            <div class="relative">
                <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                <input type="text" x-model="searchTerm" placeholder="Search products..." class="pl-10 pr-4 py-2 border rounded-md">
            </div>
            <div class="relative">
                <select x-model="categoryFilter" class="border rounded-md py-2 px-4">
                    <option value="">All Categories</option>
                    <template x-for="category in categories" :key="category">
                        <option :value="category" x-text="category"></option>
                    </template>
                </select>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="product in paginatedProducts" :key="product.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full object-cover" :src="product.image" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900" x-text="product.name"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="product.price"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="product.stock"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="product.category"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a :href="'/vendor/products/edit/' + product.id" class="text-indigo-600 hover:text-indigo-900 mr-4"><i class="fas fa-pencil-alt"></i></a>
                                <button @click="deleteProduct(product.id)" class="text-red-600 hover:text-red-900"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="flex justify-between items-center mt-4">
            <div>
                <p class="text-sm text-gray-700">
                    Showing <span x-text="(currentPage - 1) * productsPerPage + 1"></span>
                    to <span x-text="Math.min(currentPage * productsPerPage, filteredProducts.length)"></span>
                    of <span x-text="filteredProducts.length"></span> results
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button @click="currentPage = Math.max(1, currentPage - 1)" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Previous
                    </button>
                    <template x-for="i in Array.from({ length: totalPages }, (_, i) => i + 1)" :key="i">
                        <button @click="currentPage = i" :class="{'z-10 bg-indigo-50 border-indigo-500 text-indigo-600': currentPage === i}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50" x-text="i"></button>
                    </template>
                    <button @click="currentPage = Math.min(totalPages, currentPage + 1)" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Next
                    </button>
                </nav>
            </div>
        </div>
    </div>
</div>

<script>
function vendorProducts() {
    return {
        products: [],
        categories: ['Electronics', 'Clothing', 'Books', 'Home Goods'],
        searchTerm: '',
        categoryFilter: '',
        currentPage: 1,
        productsPerPage: 5,

        init() {
            this.products = [
                { id: 1, name: 'Smartphone X', price: '₹25,000', stock: 150, category: 'Electronics', image: 'https://via.placeholder.com/150' },
                { id: 2, name: 'Wireless Headphones', price: '₹4,500', stock: 300, category: 'Electronics', image: 'https://via.placeholder.com/150' },
                { id: 3, name: 'Men\'s T-Shirt', price: '₹800', stock: 500, category: 'Clothing', image: 'https://via.placeholder.com/150' },
                { id: 4, name: 'The Great Gatsby', price: '₹350', stock: 200, category: 'Books', image: 'https://via.placeholder.com/150' },
                { id: 5, name: 'Coffee Maker', price: '₹3,000', stock: 100, category: 'Home Goods', image: 'https://via.placeholder.com/150' },
                { id: 6, name: 'Laptop Pro', price: '₹85,000', stock: 80, category: 'Electronics', image: 'https://via.placeholder.com/150' },
                { id: 7, name: 'Women\'s Jeans', price: '₹2,200', stock: 250, category: 'Clothing', image: 'https://via.placeholder.com/150' },
            ];
        },

        get filteredProducts() {
            return this.products.filter(product => {
                const searchMatch = product.name.toLowerCase().includes(this.searchTerm.toLowerCase());
                const categoryMatch = this.categoryFilter ? product.category === this.categoryFilter : true;
                return searchMatch && categoryMatch;
            });
        },

        get totalPages() {
            return Math.ceil(this.filteredProducts.length / this.productsPerPage);
        },

        get paginatedProducts() {
            const start = (this.currentPage - 1) * this.productsPerPage;
            const end = start + this.productsPerPage;
            return this.filteredProducts.slice(start, end);
        },

        deleteProduct(id) {
            if (confirm('Are you sure you want to delete this product?')) {
                this.products = this.products.filter(p => p.id !== id);
            }
        }
    }
}
</script>
@endsection

