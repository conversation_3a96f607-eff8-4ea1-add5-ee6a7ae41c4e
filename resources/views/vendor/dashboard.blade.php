@extends('layouts.admin')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')
@section('page-subtitle', 'Welcome to your vendor dashboard')

@section('content')
<div x-data="vendorDashboard()" class="vendor-dashboard">
    <template x-if="loading">
        <div class="flex justify-center items-center h-64">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
    </template>

    <template x-if="!loading && dashboardData">
        <div>
            <!-- Welcome Section -->
            <div class="welcome-section">
                <div class="welcome-content">
                    <div class="welcome-badge">
                        <i class="fas fa-store"></i>
                        Vendor Dashboard
                    </div>
                    <h1 class="welcome-title">
                        Welcome back, <span style="color: var(--whamart-primary)" x-text="dashboardData.vendorName"></span>!
                    </h1>
                    <p class="welcome-subtitle">
                        Here's an overview of your store's performance. Track your sales, manage orders, and grow your business.
                    </p>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="stats-grid">
                <template x-for="stat in dashboardData.stats" :key="stat.name">
                    <div class="stat-card">
                        <div class="stat-card-content">
                            <div class="stat-icon-container" :style="{ backgroundColor: stat.color, color: stat.iconColor }">
                                <i :class="stat.icon"></i>
                            </div>
                            <div class="stat-details">
                                <p class="stat-label" x-text="stat.name"></p>
                                <p class="stat-value" x-text="stat.stat"></p>
                                <div class="stat-change positive" x-show="stat.change">
                                    <i class="fas fa-arrow-up"></i>
                                    <span x-text="stat.change"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Recent Orders -->
            <div class="content-card">
                <div class="card-header">
                    <div>
                        <h2 class="card-title">Recent Orders</h2>
                        <p class="card-subtitle">Latest orders from your customers</p>
                    </div>
                    <div class="card-actions">
                        <a href="{{ route('vendor.orders') }}" class="text-sm font-medium" style="color: var(--whamart-primary)">
                            View All Orders
                        </a>
                    </div>
                </div>
                <div class="card-content">
                    <div class="overflow-x-auto">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template x-for="order in dashboardData.recentOrders" :key="order.id">
                                    <tr>
                                        <td>
                                            <span class="font-medium" x-text="order.id"></span>
                                        </td>
                                        <td>
                                            <div class="flex items-center gap-3">
                                                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                                    <span class="text-xs font-medium" x-text="order.customer.charAt(0)"></span>
                                                </div>
                                                <span x-text="order.customer"></span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="font-medium" x-text="formatCurrency(order.amount)"></span>
                                        </td>
                                        <td>
                                            <span class="status-badge"
                                                  :class="order.status === 'Completed' ? 'completed' : order.status === 'Pending' ? 'pending' : 'cancelled'"
                                                  x-text="order.status"></span>
                                        </td>
                                        <td>
                                            <span class="text-sm text-gray-500" x-text="order.date || 'Today'"></span>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="content-card">
                <div class="card-header">
                    <div>
                        <h2 class="card-title">Quick Actions</h2>
                        <p class="card-subtitle">Frequently used actions for your store</p>
                    </div>
                </div>
                <div class="card-content">
                    <div class="quick-actions-grid">
                        <a href="{{ route('vendor.products.create') }}" class="quick-action-card">
                            <div class="quick-action-icon" style="background: rgba(126, 217, 87, 0.1); color: var(--whamart-primary)">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="quick-action-content">
                                <h3 class="quick-action-title">Add Product</h3>
                                <p class="quick-action-description">Create a new product for your store</p>
                            </div>
                        </a>

                        <a href="{{ route('vendor.orders') }}" class="quick-action-card">
                            <div class="quick-action-icon" style="background: rgba(76, 175, 80, 0.1); color: var(--whamart-secondary)">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="quick-action-content">
                                <h3 class="quick-action-title">Manage Orders</h3>
                                <p class="quick-action-description">View and process customer orders</p>
                            </div>
                        </a>

                        <a href="{{ route('vendor.analytics') }}" class="quick-action-card">
                            <div class="quick-action-icon" style="background: rgba(126, 217, 87, 0.1); color: var(--whamart-primary)">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="quick-action-content">
                                <h3 class="quick-action-title">View Analytics</h3>
                                <p class="quick-action-description">Track your store's performance</p>
                            </div>
                        </a>

                        <a href="{{ route('vendor.customers') }}" class="quick-action-card">
                            <div class="quick-action-icon" style="background: rgba(76, 175, 80, 0.1); color: var(--whamart-secondary)">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="quick-action-content">
                                <h3 class="quick-action-title">Manage Customers</h3>
                                <p class="quick-action-description">View and manage customer data</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>

<script>
function vendorDashboard() {
    return {
        loading: true,
        dashboardData: null,
        init() {
            this.fetchDashboardData();
        },
        fetchDashboardData() {
            this.loading = true;
            setTimeout(() => {
                this.dashboardData = {
                    vendorName: '{{ Auth::user()->name }}',
                    stats: [
                        {
                            name: 'Total Products',
                            stat: '24',
                            icon: 'fas fa-box',
                            color: 'rgba(126, 217, 87, 0.1)',
                            iconColor: '#7ED957',
                            change: '+12% this month'
                        },
                        {
                            name: 'Total Orders',
                            stat: '156',
                            icon: 'fas fa-shopping-bag',
                            color: 'rgba(76, 175, 80, 0.1)',
                            iconColor: '#4CAF50',
                            change: '+8% this week'
                        },
                        {
                            name: 'Total Revenue',
                            stat: '₹45,280',
                            icon: 'fas fa-rupee-sign',
                            color: 'rgba(126, 217, 87, 0.1)',
                            iconColor: '#7ED957',
                            change: '+15% this month'
                        },
                        {
                            name: 'Active Customers',
                            stat: '89',
                            icon: 'fas fa-users',
                            color: 'rgba(76, 175, 80, 0.1)',
                            iconColor: '#4CAF50',
                            change: '+5% this week'
                        }
                    ],
                    recentOrders: [
                        { id: '#WH-1234', customer: 'Karan Solanki', amount: 2500, status: 'Completed', date: '2 hours ago' },
                        { id: '#WH-1235', customer: 'John Doe', amount: 1500, status: 'Pending', date: '5 hours ago' },
                        { id: '#WH-1236', customer: 'Jane Smith', amount: 3500, status: 'Completed', date: '1 day ago' },
                        { id: '#WH-1237', customer: 'Mike Johnson', amount: 1200, status: 'Pending', date: '2 days ago' },
                        { id: '#WH-1238', customer: 'Sarah Wilson', amount: 4200, status: 'Completed', date: '3 days ago' }
                    ]
                };
                this.loading = false;
            }, 800);
        },
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(amount);
        }
    }
}
</script>
@endsection

