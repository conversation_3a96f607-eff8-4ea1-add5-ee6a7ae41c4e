@extends('layouts.admin')

@section('content')
<div x-data="vendorDashboard()" class="vendor-dashboard">
    <template x-if="loading">
        <div class="flex justify-center items-center h-64"><div class="loader"></div></div>
    </template>

    <template x-if="!loading && dashboardData">
        <div>
            <!-- Welcome Section -->
            <div class="welcome-section mb-8">
                <div class="welcome-content">
                    <h1 class="text-3xl font-bold text-gray-800">Welcome back, <span class="text-green-500" x-text="dashboardData.vendorName"></span></h1>
                    <p class="text-gray-600">Here's an overview of your WhatsApp store's performance.</p>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <template x-for="stat in dashboardData.stats" :key="stat.name">
                    <div class="stat-card bg-white p-6 rounded-lg shadow-lg">
                        <div class="flex items-center">
                            <div class="stat-icon-container p-3 rounded-full" :style="{ backgroundColor: stat.color, color: stat.iconColor }">
                                <i :class="stat.icon" class="text-2xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-gray-600 text-sm" x-text="stat.name"></p>
                                <p class="text-2xl font-bold text-gray-900" x-text="stat.stat"></p>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Recent Orders -->
            <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Orders</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Order ID</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Customer</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th></tr></thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="order in dashboardData.recentOrders" :key="order.id">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="order.id"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="order.customer"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatCurrency(order.amount)"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="order.status === 'Completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'" x-text="order.status"></span></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <a href="/vendor/products/add" class="quick-action-card bg-white p-6 rounded-lg shadow-lg flex items-center hover:bg-gray-50">
                    <i class="fas fa-plus-circle text-green-500 text-2xl"></i><span class="ml-4 text-gray-800 font-semibold">Add Product</span>
                </a>
                 <a href="/vendor/orders" class="quick-action-card bg-white p-6 rounded-lg shadow-lg flex items-center hover:bg-gray-50">
                    <i class="fas fa-box-open text-blue-500 text-2xl"></i><span class="ml-4 text-gray-800 font-semibold">Manage Orders</span>
                </a>
                 <a href="/vendor/analytics" class="quick-action-card bg-white p-6 rounded-lg shadow-lg flex items-center hover:bg-gray-50">
                    <i class="fas fa-chart-line text-purple-500 text-2xl"></i><span class="ml-4 text-gray-800 font-semibold">View Analytics</span>
                </a>
                 <a href="/vendor/customers" class="quick-action-card bg-white p-6 rounded-lg shadow-lg flex items-center hover:bg-gray-50">
                    <i class="fas fa-users text-red-500 text-2xl"></i><span class="ml-4 text-gray-800 font-semibold">Manage Customers</span>
                </a>
            </div>
        </div>
    </template>
</div>

<script>
function vendorDashboard() {
    return {
        loading: true,
        dashboardData: null,
        init() {
            this.fetchDashboardData();
        },
        fetchDashboardData() {
            this.loading = true;
            setTimeout(() => {
                this.dashboardData = {
                    vendorName: 'Whamart',
                    stats: [
                        { name: 'Total Products', stat: '1,200', icon: 'fas fa-box', color: 'rgba(37, 211, 102, 0.1)', iconColor: '#25D366' },
                        { name: 'Active Chats', stat: '350', icon: 'fas fa-comments', color: 'rgba(7, 94, 84, 0.1)', iconColor: '#075E54' },
                        { name: 'Total Revenue', stat: '₹5,40,000', icon: 'fas fa-rupee-sign', color: 'rgba(37, 211, 102, 0.1)', iconColor: '#25D366' },
                        { name: 'Total Customers', stat: '850', icon: 'fas fa-users', color: 'rgba(7, 94, 84, 0.1)', iconColor: '#075E54' }
                    ],
                    recentOrders: [
                        { id: '#1234', customer: 'Karan Solanki', amount: 2500, status: 'Completed' },
                        { id: '#1235', customer: 'John Doe', amount: 1500, status: 'Pending' },
                        { id: '#1236', customer: 'Jane Smith', amount: 3500, status: 'Completed' }
                    ]
                };
                this.loading = false;
            }, 500);
        },
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(amount);
        }
    }
}
</script>
@endsection

