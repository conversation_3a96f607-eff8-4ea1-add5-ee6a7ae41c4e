@extends('layouts.admin')

@section('content')
<div x-data="vendorServices()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Services</h1>
        <a href="{{ route('vendor.services.create') }}" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
            <i class="fas fa-plus mr-2"></i> Add Service
        </a>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <a href="{{ route('vendor.services.create') }}" class="bg-white p-6 rounded-lg shadow-lg text-center hover:shadow-xl transition-shadow">
            <i class="fas fa-plus-circle text-3xl text-green-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Add New Service</h3>
            <p class="text-gray-600 text-sm">Offer a new service</p>
        </a>
        <a href="#" class="bg-white p-6 rounded-lg shadow-lg text-center hover:shadow-xl transition-shadow">
            <i class="fas fa-tags text-3xl text-blue-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Manage Categories</h3>
            <p class="text-gray-600 text-sm">Organize your services</p>
        </a>
        <a href="#" class="bg-white p-6 rounded-lg shadow-lg text-center hover:shadow-xl transition-shadow">
            <i class="fas fa-file-import text-3xl text-purple-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Import Services</h3>
            <p class="text-gray-600 text-sm">Bulk upload from a CSV file</p>
        </a>
    </div>

    <!-- Services Table -->
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex justify-between items-center mb-4">
            <div class="relative">
                <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                <input type="text" x-model="searchTerm" placeholder="Search services..." class="pl-10 pr-4 py-2 border rounded-md">
            </div>
            <div class="relative">
                <select x-model="categoryFilter" class="border rounded-md py-2 px-4">
                    <option value="">All Categories</option>
                    <template x-for="category in categories" :key="category">
                        <option :value="category" x-text="category"></option>
                    </template>
                </select>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="service in paginatedServices" :key="service.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900" x-text="service.name"></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="service.price"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="service.duration"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="service.category"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a :href="'/vendor/services/edit/' + service.id" class="text-indigo-600 hover:text-indigo-900 mr-4"><i class="fas fa-pencil-alt"></i></a>
                                <button @click="deleteService(service.id)" class="text-red-600 hover:text-red-900"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="flex justify-between items-center mt-4">
            <div>
                <p class="text-sm text-gray-700">
                    Showing <span x-text="(currentPage - 1) * servicesPerPage + 1"></span>
                    to <span x-text="Math.min(currentPage * servicesPerPage, filteredServices.length)"></span>
                    of <span x-text="filteredServices.length"></span> results
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button @click="currentPage = Math.max(1, currentPage - 1)" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Previous
                    </button>
                    <template x-for="i in Array.from({ length: totalPages }, (_, i) => i + 1)" :key="i">
                        <button @click="currentPage = i" :class="{'z-10 bg-indigo-50 border-indigo-500 text-indigo-600': currentPage === i}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50" x-text="i"></button>
                    </template>
                    <button @click="currentPage = Math.min(totalPages, currentPage + 1)" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        Next
                    </button>
                </nav>
            </div>
        </div>
    </div>
</div>

<script>
function vendorServices() {
    return {
        services: [],
        categories: ['Consulting', 'Development', 'Design', 'Marketing'],
        searchTerm: '',
        categoryFilter: '',
        currentPage: 1,
        servicesPerPage: 5,

        init() {
            this.services = [
                { id: 1, name: 'Web Development', price: '₹50,000', duration: '4 weeks', category: 'Development' },
                { id: 2, name: 'SEO Consulting', price: '₹15,000', duration: '1 month', category: 'Marketing' },
                { id: 3, name: 'Logo Design', price: '₹10,000', duration: '1 week', category: 'Design' },
                { id: 4, name: 'Business Consulting', price: '₹25,000', duration: '2 weeks', category: 'Consulting' },
                { id: 5, name: 'Social Media Marketing', price: '₹20,000', duration: '1 month', category: 'Marketing' },
            ];
        },

        get filteredServices() {
            return this.services.filter(service => {
                const searchMatch = service.name.toLowerCase().includes(this.searchTerm.toLowerCase());
                const categoryMatch = this.categoryFilter ? service.category === this.categoryFilter : true;
                return searchMatch && categoryMatch;
            });
        },

        get totalPages() {
            return Math.ceil(this.filteredServices.length / this.servicesPerPage);
        },

        get paginatedServices() {
            const start = (this.currentPage - 1) * this.servicesPerPage;
            const end = start + this.servicesPerPage;
            return this.filteredServices.slice(start, end);
        },

        deleteService(id) {
            if (confirm('Are you sure you want to delete this service?')) {
                this.services = this.services.filter(s => s.id !== id);
            }
        }
    }
}
</script>
@endsection

