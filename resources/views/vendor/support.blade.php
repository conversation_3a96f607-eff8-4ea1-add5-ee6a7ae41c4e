@extends('layouts.admin')

@section('content')
<div x-data="vendorSupport()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Help & Support</h1>

    <!-- Contact Methods -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-lg text-center">
            <i class="fas fa-envelope text-3xl text-green-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Email Support</h3>
            <p class="text-gray-600 text-sm"><EMAIL></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg text-center">
            <i class="fas fa-phone text-3xl text-green-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Phone Support</h3>
            <p class="text-gray-600 text-sm">+91 ************</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg text-center">
            <i class="fas fa-comments text-3xl text-green-500 mx-auto mb-4"></i>
            <h3 class="font-semibold text-lg">Live Chat</h3>
            <p class="text-gray-600 text-sm">Available 24/7</p>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- FAQ Section -->
        <div class="lg:col-span-2">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Frequently Asked Questions</h2>
                <div class="space-y-4">
                    <template x-for="(faq, index) in faqs" :key="index">
                        <div>
                            <button @click="toggleFaq(index)" class="w-full flex justify-between items-center text-left font-semibold">
                                <span x-text="faq.question"></span>
                                <i :class="openFaq === index ? 'fa-chevron-up' : 'fa-chevron-down'" class="fas"></i>
                            </button>
                            <div x-show="openFaq === index" x-transition class="mt-2 text-gray-600">
                                <p x-text="faq.answer"></p>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- Support Request Form -->
        <div class="lg:col-span-1">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Submit a Request</h2>
                <form @submit.prevent="handleSubmit">
                    <div class="space-y-4">
                        <div><label for="name" class="block text-sm font-medium">Name</label><input type="text" id="name" x-model="formData.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label for="email" class="block text-sm font-medium">Email</label><input type="email" id="email" x-model="formData.email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label for="subject" class="block text-sm font-medium">Subject</label><input type="text" id="subject" x-model="formData.subject" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label for="message" class="block text-sm font-medium">Message</label><textarea id="message" x-model="formData.message" rows="4" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></textarea></div>
                    </div>
                    <div class="mt-6">
                        <button type="submit" class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function vendorSupport() {
    return {
        openFaq: null,
        formData: { name: '', email: '', subject: '', message: '' },
        faqs: [
            { question: 'How do I add products?', answer: 'Navigate to Products > Add New Product and fill in the details.' },
            { question: 'How do I process orders?', answer: 'Go to the Orders section to view and manage all customer orders.' },
            { question: 'How do payments work?', answer: 'Payments are processed securely and transferred to your linked bank account within 2-3 business days.' }
        ],

        toggleFaq(index) {
            this.openFaq = this.openFaq === index ? null : index;
        },

        handleSubmit() {
            alert('Support request submitted!');
            this.formData = { name: '', email: '', subject: '', message: '' };
        }
    }
}
</script>
@endsection

