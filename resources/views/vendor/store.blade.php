@extends('layouts.admin')

@section('content')
<div x-data="vendorStore()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">My Store</h1>

    <!-- Store Information -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Store Details</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="md:col-span-1">
                <label class="block text-sm font-medium text-gray-700">Store Logo</label>
                <div class="mt-1 flex items-center">
                    <template x-if="!storeData.logoPreview">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-store text-gray-400 text-3xl"></i>
                        </div>
                    </template>
                    <template x-if="storeData.logoPreview">
                        <img :src="storeData.logoPreview" alt="Logo Preview" class="w-24 h-24 rounded-full object-cover">
                    </template>
                    <div class="ml-4">
                        <input type="file" @change="handleLogoUpload" x-ref="logoInput" class="hidden">
                        <button @click="$refs.logoInput.click()" type="button" class="px-3 py-2 bg-gray-200 text-sm font-medium text-gray-800 rounded-md hover:bg-gray-300">Change</button>
                        <template x-if="storeData.logoPreview">
                            <button @click="removeLogo" type="button" class="ml-2 px-3 py-2 bg-red-100 text-sm font-medium text-red-700 rounded-md hover:bg-red-200">Remove</button>
                        </template>
                    </div>
                </div>
            </div>
            <div class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div><label for="name" class="block text-sm font-medium text-gray-700">Store Name</label><input type="text" id="name" x-model="storeData.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                <div><label for="category" class="block text-sm font-medium text-gray-700">Business Category</label><select id="category" x-model="storeData.businessCategory" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"><option value="fashion">Fashion</option><option value="electronics">Electronics</option><option value="grocery">Grocery</option></select></div>
                <div class="md:col-span-2"><label for="description" class="block text-sm font-medium text-gray-700">Description</label><textarea id="description" x-model="storeData.description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></textarea></div>
            </div>
        </div>
        <div class="flex justify-end"><button @click="saveStoreInfo" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">Save Changes</button></div>
    </div>

    <!-- Payment Methods -->
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-gray-800">Payment Methods</h2>
            <button @click="openPaymentModal()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">Add Method</button>
        </div>
        <div class="space-y-4">
            <template x-for="method in paymentMethods" :key="method.id">
                <div class="p-4 border rounded-lg flex justify-between items-center">
                    <div>
                        <p class="font-semibold" x-text="method.name"></p>
                        <p class="text-sm text-gray-600" x-text="method.type === 'upi' ? method.upiId : method.accountNumber"></p>
                    </div>
                    <div>
                        <button @click="openPaymentModal(method)" class="text-blue-500 hover:underline">Edit</button>
                        <button @click="deletePaymentMethod(method.id)" class="ml-4 text-red-500 hover:underline">Delete</button>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Payment Method Modal -->
    <div x-show="showPaymentModal" class="fixed inset-0 bg-gray-800 bg-opacity-50 flex items-center justify-center" x-cloak>
        <div class="bg-white p-8 rounded-lg shadow-xl w-full max-w-md" @click.away="showPaymentModal = false">
            <h3 class="text-lg font-bold mb-4" x-text="isEditingPaymentMethod ? 'Edit Payment Method' : 'Add Payment Method'"></h3>
            <div class="space-y-4">
                <div><label class="block text-sm font-medium">Type</label><select x-model="currentPaymentMethod.type" class="mt-1 block w-full rounded-md border-gray-300"><option value="upi">UPI</option><option value="bank_transfer">Bank Transfer</option></select></div>
                <div><label class="block text-sm font-medium">Display Name</label><input type="text" x-model="currentPaymentMethod.name" class="mt-1 block w-full rounded-md border-gray-300"></div>
                <template x-if="currentPaymentMethod.type === 'upi'">
                    <div><label class="block text-sm font-medium">UPI ID</label><input type="text" x-model="currentPaymentMethod.upiId" class="mt-1 block w-full rounded-md border-gray-300"></div>
                </template>
                <template x-if="currentPaymentMethod.type === 'bank_transfer'">
                    <div class="space-y-4">
                        <div><label class="block text-sm font-medium">Account Number</label><input type="text" x-model="currentPaymentMethod.accountNumber" class="mt-1 block w-full rounded-md border-gray-300"></div>
                        <div><label class="block text-sm font-medium">IFSC Code</label><input type="text" x-model="currentPaymentMethod.ifscCode" class="mt-1 block w-full rounded-md border-gray-300"></div>
                    </div>
                </template>
            </div>
            <div class="mt-6 flex justify-end space-x-4">
                <button @click="showPaymentModal = false" class="px-4 py-2 bg-gray-200 rounded-md">Cancel</button>
                <button @click="savePaymentMethod" class="px-4 py-2 bg-green-600 text-white rounded-md" x-text="isEditingPaymentMethod ? 'Update' : 'Add'"></button>
            </div>
        </div>
    </div>
</div>

<script>
function vendorStore() {
    return {
        storeData: { name: '', description: '', businessCategory: 'fashion', logo: null, logoPreview: null },
        paymentMethods: [],
        showPaymentModal: false,
        isEditingPaymentMethod: false,
        currentPaymentMethod: { id: null, type: 'upi', name: '', upiId: '', accountNumber: '', ifscCode: '' },

        init() {
            this.fetchStoreData();
            this.fetchPaymentMethods();
        },

        fetchStoreData() {
            setTimeout(() => {
                this.storeData = { name: 'My Awesome Store', description: 'Selling the best products.', businessCategory: 'fashion', logo: null, logoPreview: 'https://via.placeholder.com/150' };
            }, 500);
        },

        fetchPaymentMethods() {
            setTimeout(() => {
                this.paymentMethods = [
                    { id: 1, type: 'upi', name: 'My UPI', upiId: 'vendor@upi' },
                    { id: 2, type: 'bank_transfer', name: 'Business Account', accountNumber: '...1234', ifscCode: 'BANK000001' }
                ];
            }, 500);
        },

        handleLogoUpload(event) {
            const file = event.target.files[0];
            if (file) {
                this.storeData.logo = file;
                this.storeData.logoPreview = URL.createObjectURL(file);
            }
        },

        removeLogo() {
            this.storeData.logo = null;
            this.storeData.logoPreview = null;
        },

        saveStoreInfo() {
            console.log('Saving store info:', this.storeData);
            alert('Store information saved!');
        },

        openPaymentModal(method = null) {
            if (method) {
                this.isEditingPaymentMethod = true;
                this.currentPaymentMethod = { ...method };
            } else {
                this.isEditingPaymentMethod = false;
                this.currentPaymentMethod = { id: null, type: 'upi', name: '', upiId: '', accountNumber: '', ifscCode: '' };
            }
            this.showPaymentModal = true;
        },

        savePaymentMethod() {
            if (this.isEditingPaymentMethod) {
                const index = this.paymentMethods.findIndex(m => m.id === this.currentPaymentMethod.id);
                this.paymentMethods[index] = this.currentPaymentMethod;
            } else {
                this.currentPaymentMethod.id = Date.now();
                this.paymentMethods.push(this.currentPaymentMethod);
            }
            this.showPaymentModal = false;
        },

        deletePaymentMethod(id) {
            this.paymentMethods = this.paymentMethods.filter(m => m.id !== id);
        }
    }
}
</script>
@endsection

