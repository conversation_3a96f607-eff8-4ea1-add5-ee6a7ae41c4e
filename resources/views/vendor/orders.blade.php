@extends('layouts.admin')

@section('content')
<div x-data="vendorOrders()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Orders Management</h1>

    <template x-if="loading">
        <div class="flex justify-center items-center h-64"><div class="loader"></div></div>
    </template>

    <template x-if="!loading && orders.length > 0">
        <div>
            <!-- Order Summary -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Completed Orders</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="statusCounts.completed"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Processing Orders</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="statusCounts.processing"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Shipped Orders</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="statusCounts.shipped"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Pending Orders</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="statusCounts.pending"></p></div>
            </div>

            <!-- Order List -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">All Orders</h2>
                <div class="flex items-center justify-between mb-4">
                    <input type="text" x-model="searchTerm" placeholder="Search by Order ID or Customer" class="w-full md:w-1/3 px-4 py-2 border border-gray-300 rounded-lg">
                    <select x-model="statusFilter" class="ml-4 px-4 py-2 border border-gray-300 rounded-lg">
                        <option value="All">All Statuses</option>
                        <option value="Completed">Completed</option>
                        <option value="Processing">Processing</option>
                        <option value="Shipped">Shipped</option>
                        <option value="Pending">Pending</option>
                    </select>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Order ID</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Customer</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th></tr></thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="order in paginatedOrders" :key="order.id">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="order.id"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="order.customerName"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="order.date"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatCurrency(order.total)"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="statusColor(order.status)" x-text="order.status"></span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><a href="#" class="text-indigo-600 hover:text-indigo-900">View</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <!-- Pagination -->
                <div class="mt-4 flex justify-between items-center">
                    <p class="text-sm text-gray-700">Showing <span x-text="(currentPage - 1) * itemsPerPage + 1"></span> to <span x-text="Math.min(currentPage * itemsPerPage, filteredOrders.length)"></span> of <span x-text="filteredOrders.length"></span> results</p>
                    <div>
                        <button @click="currentPage = Math.max(1, currentPage - 1)" :disabled="currentPage === 1" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Prev</button>
                        <button @click="currentPage = Math.min(totalPages, currentPage + 1)" :disabled="currentPage === totalPages" class="ml-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>

<script>
function vendorOrders() {
    return {
        loading: true,
        orders: [],
        searchTerm: '',
        statusFilter: 'All',
        currentPage: 1,
        itemsPerPage: 10,

        init() {
            this.fetchData();
        },

        fetchData() {
            this.loading = true;
            setTimeout(() => {
                const statuses = ['Completed', 'Processing', 'Shipped', 'Pending'];
                this.orders = Array.from({ length: 150 }, (_, i) => ({
                    id: `#WH${1001 + i}`,
                    customerName: `Customer ${i + 1}`,
                    date: new Date(2023, 0, i + 1).toISOString().split('T')[0],
                    total: Math.floor(500 + Math.random() * 2000),
                    status: statuses[i % statuses.length]
                }));
                this.loading = false;
            }, 500);
        },

        get filteredOrders() {
            return this.orders.filter(order => {
                const matchesSearch = order.id.toLowerCase().includes(this.searchTerm.toLowerCase()) || order.customerName.toLowerCase().includes(this.searchTerm.toLowerCase());
                const matchesStatus = this.statusFilter === 'All' || order.status === this.statusFilter;
                return matchesSearch && matchesStatus;
            });
        },

        get totalPages() {
            return Math.ceil(this.filteredOrders.length / this.itemsPerPage);
        },

        get paginatedOrders() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredOrders.slice(start, end);
        },

        get statusCounts() {
            return this.orders.reduce((acc, order) => {
                acc[order.status.toLowerCase()] = (acc[order.status.toLowerCase()] || 0) + 1;
                return acc;
            }, { completed: 0, processing: 0, shipped: 0, pending: 0 });
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(amount);
        },

        statusColor(status) {
            switch (status) {
                case 'Completed': return 'bg-green-100 text-green-800';
                case 'Processing': return 'bg-yellow-100 text-yellow-800';
                case 'Shipped': return 'bg-blue-100 text-blue-800';
                case 'Pending': return 'bg-gray-100 text-gray-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }
    }
}
</script>
@endsection

