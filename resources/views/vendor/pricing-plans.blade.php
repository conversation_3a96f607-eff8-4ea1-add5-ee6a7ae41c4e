@extends('layouts.admin')

@section('content')
<div x-data="pricingPlans()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Subscription Plans</h1>

    <template x-if="loading">
        <div class="flex justify-center items-center h-64"><div class="loader"></div></div>
    </template>

    <template x-if="!loading && plans.length > 0">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <template x-for="plan in plans" :key="plan.id">
                <div class="bg-white p-8 rounded-lg shadow-lg flex flex-col" :class="{ 'border-2 border-green-500': currentPlanId === plan.id }">
                    <h2 class="text-2xl font-bold text-gray-800" x-text="plan.name"></h2>
                    <p class="text-gray-500 mt-2" x-text="plan.description"></p>
                    <div class="my-6">
                        <span class="text-4xl font-bold text-gray-900" x-text="formatCurrency(plan.price)"></span>
                        <span class="text-gray-500">/year</span>
                    </div>
                    <ul class="space-y-4 text-gray-700 mb-8">
                        <template x-for="feature in plan.features" :key="feature">
                            <li class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                <span x-text="feature"></span>
                            </li>
                        </template>
                    </ul>
                    <div class="mt-auto">
                        <button @click="handleUpgrade(plan.id)" 
                                :disabled="currentPlanId === plan.id" 
                                class="w-full py-3 px-6 rounded-lg text-white font-semibold transition-colors"
                                :class="currentPlanId === plan.id ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-500 hover:bg-green-600'">
                            <span x-text="currentPlanId === plan.id ? 'Current Plan' : 'Upgrade to ' + plan.name"></span>
                        </button>
                    </div>
                </div>
            </template>
        </div>
    </template>
</div>

<script>
function pricingPlans() {
    return {
        loading: true,
        plans: [],
        currentPlanId: 2, // Mock current plan

        init() {
            this.fetchData();
        },

        fetchData() {
            this.loading = true;
            setTimeout(() => {
                this.plans = [
                    {
                        id: 1,
                        name: 'Basic',
                        description: 'For new vendors getting started.',
                        price: 999,
                        features: ['50 Product Listings', 'Basic Analytics', 'Email Support']
                    },
                    {
                        id: 2,
                        name: 'Pro',
                        description: 'For growing businesses that need more.',
                        price: 2999,
                        features: ['Unlimited Product Listings', 'Advanced Analytics', 'Priority Email Support', 'Custom Domain']
                    },
                    {
                        id: 3,
                        name: 'Enterprise',
                        description: 'For large-scale operations.',
                        price: 7999,
                        features: ['All Pro Features', 'Dedicated Account Manager', '24/7 Phone Support', 'API Access']
                    }
                ];
                this.loading = false;
            }, 500);
        },

        handleUpgrade(planId) {
            if (this.currentPlanId === planId) return;
            alert(`Upgrading to plan ID: ${planId}`);
            // Here you would typically redirect to a payment gateway
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', minimumFractionDigits: 0 }).format(amount);
        }
    }
}
</script>
@endsection

