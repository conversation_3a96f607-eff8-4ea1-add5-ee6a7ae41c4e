@extends('layouts.admin')

@section('content')
<div x-data="vendorAnalytics()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Store Analytics</h1>

    <!-- Time Range Selector -->
    <div class="mb-6 flex justify-end">
        <select x-model="timeRange" @change="fetchAnalyticsData()" class="px-4 py-2 border border-gray-300 rounded-lg">
            <option value="day">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
        </select>
    </div>

    <template x-if="loading">
        <div class="flex justify-center items-center h-64"><div class="loader"></div></div>
    </template>

    <template x-if="!loading && analyticsData">
        <div>
            <!-- Overview Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <template x-for="metric in analyticsData.overview" :key="metric.name">
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <p class="text-gray-600 text-sm" x-text="metric.name"></p>
                        <p class="text-3xl font-bold text-gray-900 mt-2" x-text="metric.value"></p>
                        <p class="text-sm mt-1" :class="metric.trend === 'up' ? 'text-green-600' : 'text-red-600'" x-text="`${metric.change} vs last period`"></p>
                    </div>
                </template>
            </div>

            <!-- Sales Chart and Top Products -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Monthly Sales Chart -->
                <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-lg">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Monthly Sales</h2>
                    <canvas id="salesChart"></canvas>
                </div>
                <!-- Top Products -->
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Top Products</h2>
                    <ul>
                        <template x-for="product in analyticsData.topProducts" :key="product.id">
                            <li class="flex justify-between items-center py-2 border-b">
                                <span class="text-gray-800" x-text="product.name"></span>
                                <span class="font-bold text-gray-900" x-text="product.sales"></span>
                            </li>
                        </template>
                    </ul>
                </div>
            </div>
        </div>
    </template>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function vendorAnalytics() {
    return {
        loading: true,
        timeRange: 'month',
        analyticsData: null,
        salesChart: null,

        init() {
            this.fetchAnalyticsData();
        },

        fetchAnalyticsData() {
            this.loading = true;
            setTimeout(() => {
                this.analyticsData = {
                    overview: [
                        { name: 'Total Revenue', value: '₹8,50,000', change: '+12%', trend: 'up' },
                        { name: 'Total Orders', value: '1,250', change: '+8%', trend: 'up' },
                        { name: 'Avg. Order Value', value: '₹680', change: '-2%', trend: 'down' },
                        { name: 'Conversion Rate', value: '4.2%', change: '+0.5%', trend: 'up' }
                    ],
                    monthlySales: [120000, 150000, 180000, 160000, 200000, 220000, 250000, 230000, 260000, 280000, 300000, 320000],
                    topProducts: [
                        { id: 1, name: 'Product A', sales: '500 units' },
                        { id: 2, name: 'Product B', sales: '450 units' },
                        { id: 3, name: 'Product C', sales: '400 units' },
                        { id: 4, name: 'Product D', sales: '350 units' },
                        { id: 5, name: 'Product E', sales: '300 units' }
                    ]
                };
                this.loading = false;
                this.$nextTick(() => this.renderSalesChart());
            }, 500);
        },

        renderSalesChart() {
            const ctx = document.getElementById('salesChart').getContext('2d');
            if (this.salesChart) {
                this.salesChart.destroy();
            }
            this.salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Sales',
                        data: this.analyticsData.monthlySales,
                        borderColor: 'rgba(37, 211, 102, 1)',
                        backgroundColor: 'rgba(37, 211, 102, 0.2)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    }
}
</script>
@endsection

