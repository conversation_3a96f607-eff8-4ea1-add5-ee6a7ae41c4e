@extends('layouts.admin')

@section('content')
<div x-data="vendorSettings()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Settings</h1>

    <form @submit.prevent="handleSubmit">
        <!-- Personal Information -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Personal Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div><label for="name" class="block text-sm font-medium text-gray-700">Full Name</label><input type="text" id="name" x-model="formData.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                <div><label for="email" class="block text-sm font-medium text-gray-700">Email Address</label><input type="email" id="email" x-model="formData.email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                <div><label for="phone" class="block text-sm font-medium text-gray-700">Phone</label><input type="text" id="phone" x-model="formData.phone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
            </div>
        </div>

        <!-- Subscription Plan -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Subscription Plan</h2>
            <template x-if="loading">
                <p>Loading subscription details...</p>
            </template>
            <template x-if="!loading">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-lg font-semibold text-gray-900" x-text="subscriptionPlan.name"></p>
                        <p class="text-sm text-gray-500">Billed annually. Renews on <span x-text="formatDate(subscriptionPlan.endDate)"></span>.</p>
                    </div>
                    <a href="{{ route('vendor.pricing-plans') }}" class="text-green-600 hover:text-green-800 font-semibold">Change Plan</a>
                </div>
            </template>
        </div>

        <!-- Preferences -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Preferences</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="language" class="block text-sm font-medium text-gray-700">Language</label>
                    <select id="language" x-model="formData.language" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <option value="en">English</option>
                        <option value="hi">Hindi</option>
                    </select>
                </div>
                <div>
                    <label for="currency" class="block text-sm font-medium text-gray-700">Currency</label>
                    <select id="currency" x-model="formData.currency" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                        <option value="INR">INR</option>
                        <option value="USD">USD</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Notifications -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Notifications</h2>
            <div class="flex items-center">
                <input type="checkbox" id="notifications" x-model="formData.notifications" class="h-4 w-4 text-green-600 border-gray-300 rounded">
                <label for="notifications" class="ml-2 block text-sm text-gray-900">Receive email notifications for new orders and messages.</label>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-4">
            <button type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">Cancel</button>
            <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">Save Changes</button>
        </div>

        <!-- Success Message -->
        <template x-if="successMessage">
            <div x-transition class="mt-4 p-4 bg-green-100 text-green-800 rounded-lg" x-text="successMessage"></div>
        </template>
    </form>
</div>

<script>
function vendorSettings() {
    return {
        loading: true,
        formData: {
            name: 'Current Vendor Name',
            email: '<EMAIL>',
            phone: '+91 98765 43210',
            language: 'en',
            currency: 'INR',
            notifications: true,
        },
        subscriptionPlan: {},
        successMessage: '',

        init() {
            this.fetchSubscription();
        },

        fetchSubscription() {
            this.loading = true;
            setTimeout(() => {
                this.subscriptionPlan = {
                    name: 'Pro Plan',
                    endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
                };
                this.loading = false;
            }, 500);
        },

        handleSubmit() {
            console.log('Form submitted:', this.formData);
            this.successMessage = 'Settings saved successfully!';
            setTimeout(() => this.successMessage = '', 3000);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-IN', { year: 'numeric', month: 'long', day: 'numeric' });
        }
    }
}
</script>
@endsection

