<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\Admin\StoreController as AdminStoreController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\AcademyController;
use App\Http\Controllers\Admin\VendorController;
use App\Http\Controllers\Admin\SubscriptionController as AdminSubscriptionController;
use App\Http\Controllers\Admin\MessageTemplateController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\Public\PageController;
use App\Http\Controllers\Public\StoreController as PublicStoreControllerAlias;
use App\Http\Controllers\PublicStoreController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\ChatFlowController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\PaymentVerificationController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\SubscriptionController as MainSubscriptionController;
use App\Http\Controllers\Influencer\DashboardController as InfluencerDashboardController;
use App\Http\Controllers\Investor\DashboardController as InvestorDashboardController;
use App\Http\Controllers\Vendor\DashboardController as VendorDashboardController;

// Public routes
Route::get('/', function () {
    return view('home');
})->name('home');
Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [AuthController::class, 'register']);
Route::get('/login', [AuthController::class, 'create'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/store/{storeUrl}', [PublicStoreController::class, 'getStoreByUrl'])->name('store.public.view');
Route::post('/store/{storeUrl}/checkout', [CheckoutController::class, 'placeOrder'])->name('store.public.checkout');
Route::get('/chat-flows/store/{storeUrl}/default', [ChatFlowController::class, 'getDefaultChatFlowByStoreUrl'])->name('chat-flows.default');
Route::get('/stores/{storeId}/services', [ServiceController::class, 'getServicesByStoreId'])->name('stores.services');

// Subscription Plans
Route::get('/subscription-plans', [MainSubscriptionController::class, 'getAllPlans'])->name('plans.index');
Route::get('/subscription-plans/{id}', [MainSubscriptionController::class, 'getPlanById'])->name('plans.show');

// Authenticated routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/me', [AuthController::class, 'me']);

    // User Subscriptions
    Route::get('/subscription', [MainSubscriptionController::class, 'getCurrentSubscription'])->name('subscription.current');
    Route::post('/subscriptions', [MainSubscriptionController::class, 'createSubscription'])->name('subscription.create');
    Route::patch('/subscriptions/{id}', [MainSubscriptionController::class, 'updateSubscriptionStatus'])->name('subscription.update');

    // User profile routes
    Route::put('/user/profile', [UserController::class, 'updateProfile'])->name('user.profile.update');
    Route::put('/user/password', [UserController::class, 'changePassword'])->name('user.password.update');



    // Vendor-only routes
    Route::middleware('isVendor')->group(function () {
        // Dashboard analytics and store information routes
        Route::get('/dashboard/stats', [\App\Http\Controllers\AnalyticsController::class, 'getAnalyticsOverview']);
        Route::get('/stores/info', [\App\Http\Controllers\StoreController::class, 'getStoreInfo']);
        
        Route::prefix('store')->name('store.')->group(function () {
            Route::post('/info', [StoreController::class, 'updateStoreInfo'])->name('info.update'); // Using POST for file uploads
        });

        Route::apiResource('products', ProductController::class);
        Route::apiResource('customers', CustomerController::class);
        Route::apiResource('orders', OrderController::class)->only(['index', 'show', 'update']);



        Route::apiResource('chat-flows', ChatFlowController::class);

        Route::apiResource('payment-methods', PaymentMethodController::class);

        Route::apiResource('services', ServiceController::class);
    });

        // Admin-only routes
        Route::middleware('isAdmin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'dashboard'])->name('admin.dashboard');
        Route::get('/academy', [AcademyController::class, 'index'])->name('admin.academy');
        Route::get('/vendors', [VendorController::class, 'index'])->name('admin.vendors');
        Route::get('analytics', [DashboardController::class, 'analytics'])->name('analytics');
        Route::get('investment', [DashboardController::class, 'investment'])->name('investment');
        Route::get('message-templates', [DashboardController::class, 'messageTemplates'])->name('message-templates');
        Route::get('permissions', [DashboardController::class, 'permissions'])->name('permissions');
        Route::get('settings', [DashboardController::class, 'settings'])->name('settings');
        Route::get('subscriptions', [DashboardController::class, 'subscriptions'])->name('subscriptions');
        Route::get('support', [DashboardController::class, 'support'])->name('support');
        Route::get('users', [DashboardController::class, 'users'])->name('users');
        Route::get('vendors', [DashboardController::class, 'vendors'])->name('vendors');
        Route::get('investor-management', [DashboardController::class, 'investorManagement'])->name('investor-management');
        Route::get('marketing', [DashboardController::class, 'marketing'])->name('marketing');
        Route::apiResource('users', UserController::class);
        Route::get('stores', [AdminStoreController::class, 'index'])->name('stores.index');
        Route::put('stores/{store}/verify', [AdminStoreController::class, 'verifyStore'])->name('stores.verify');
    });

    // Influencer-only routes
    Route::middleware('isInfluencer')->prefix('influencer')->name('influencer.')->group(function () {
        Route::get('dashboard', [InfluencerDashboardController::class, 'dashboard'])->name('dashboard');
        Route::get('campaigns', [InfluencerDashboardController::class, 'campaigns'])->name('campaigns');
        Route::get('content', [InfluencerDashboardController::class, 'content'])->name('content');
        Route::get('earnings', [InfluencerDashboardController::class, 'earnings'])->name('earnings');
        Route::get('performance', [InfluencerDashboardController::class, 'performance'])->name('performance');
        Route::get('profile', [InfluencerDashboardController::class, 'profile'])->name('profile');
        Route::get('promo-material', [InfluencerDashboardController::class, 'promoMaterial'])->name('promo-material');
        Route::get('referral-links', [InfluencerDashboardController::class, 'referralLinks'])->name('referral-links');
        Route::get('support', [InfluencerDashboardController::class, 'support'])->name('support');
        Route::get('withdrawals', [InfluencerDashboardController::class, 'withdrawals'])->name('withdrawals');
    });

    // Investor-only routes
    Route::middleware('isInvestor')->prefix('investor')->name('investor.')->group(function () {
        Route::get('dashboard', [InvestorDashboardController::class, 'dashboard'])->name('dashboard');
        Route::get('capital', [InvestorDashboardController::class, 'capital'])->name('capital');
        Route::get('equity', [InvestorDashboardController::class, 'equity'])->name('equity');
        Route::get('investments', [InvestorDashboardController::class, 'investments'])->name('investments');
        Route::get('management', [InvestorDashboardController::class, 'management'])->name('management');
        Route::get('market-expansion', [InvestorDashboardController::class, 'marketExpansion'])->name('market-expansion');
        Route::get('performance', [InvestorDashboardController::class, 'performance'])->name('performance');
        Route::get('profile', [InvestorDashboardController::class, 'profile'])->name('profile');
        Route::get('projections', [InvestorDashboardController::class, 'projections'])->name('projections');
        Route::get('revenue-analytics', [InvestorDashboardController::class, 'revenueAnalytics'])->name('revenue-analytics');
        Route::get('roadmap', [InvestorDashboardController::class, 'roadmap'])->name('roadmap');
        Route::get('vendor-analytics', [InvestorDashboardController::class, 'vendorAnalytics'])->name('vendor-analytics');
    });

    // Vendor-only routes
    Route::middleware('isVendor')->prefix('vendor')->name('vendor.')->group(function () {
        Route::get('dashboard', [VendorDashboardController::class, 'dashboard'])->name('dashboard');
        Route::get('analytics', [VendorDashboardController::class, 'analytics'])->name('analytics');
        Route::get('customers', [VendorDashboardController::class, 'customers'])->name('customers');
        Route::get('discounts', [VendorDashboardController::class, 'discounts'])->name('discounts');
        Route::get('inventory', [VendorDashboardController::class, 'inventory'])->name('inventory');
        Route::get('marketing', [VendorDashboardController::class, 'marketing'])->name('marketing');
        Route::get('orders', [VendorDashboardController::class, 'orders'])->name('orders');
        Route::get('products', [VendorDashboardController::class, 'products'])->name('products');
        Route::get('profile', [VendorDashboardController::class, 'profile'])->name('profile');
        Route::get('reports', [VendorDashboardController::class, 'reports'])->name('reports');
        Route::get('reviews', [VendorDashboardController::class, 'reviews'])->name('reviews');
        Route::get('settings', [VendorDashboardController::class, 'settings'])->name('settings');
        Route::get('shipping', [VendorDashboardController::class, 'shipping'])->name('shipping');
        Route::get('staff', [VendorDashboardController::class, 'staff'])->name('staff');
        Route::get('store-settings', [VendorDashboardController::class, 'storeSettings'])->name('store-settings');
        Route::get('support', [VendorDashboardController::class, 'support'])->name('support');
        Route::get('chat-flow/builder', [VendorDashboardController::class, 'chatFlowBuilder'])->name('chat-flow.builder');
        Route::get('chat-flow/list', [VendorDashboardController::class, 'chatFlowList'])->name('chat-flow.list');
    });
});

// API health check
Route::get('/api/health', function () {
    return response()->json([
        'status' => 'ok',
        'message' => 'Whamart API is running',
        'environment' => app()->environment(),
        'timestamp' => now()
    ]);
});

// Special routes to match frontend API calls with various prefixes
Route::middleware('auth:sanctum')->group(function () {
    // Support both with and without /api prefix for dashboard stats
    Route::get('/api/dashboard/stats', [\App\Http\Controllers\AnalyticsController::class, 'getAnalyticsOverview']);
    Route::get('/dashboard/stats', [\App\Http\Controllers\AnalyticsController::class, 'getAnalyticsOverview']);
    
    // Support both with and without /api prefix for store info
    Route::get('/api/stores/info', [\App\Http\Controllers\StoreController::class, 'getStoreInfo']);
    Route::get('/stores/info', [\App\Http\Controllers\StoreController::class, 'getStoreInfo']);
});

// Public Pages
// Route::get('/', [PageController::class, 'home'])->name('home'); // This was overriding the main view
Route::get('/about-us', [PageController::class, 'about'])->name('about-us');
Route::get('/blog', [PageController::class, 'blog'])->name('blog');
Route::get('/careers', [PageController::class, 'careers'])->name('careers');
Route::get('/contact-us', [PageController::class, 'contact'])->name('public.contact');
Route::post('/contact-us', [PageController::class, 'handleContactForm'])->name('public.contact.submit');
Route::get('/help-center', [PageController::class, 'helpCenter'])->name('help-center');
Route::get('/press', [PageController::class, 'press'])->name('press');
Route::get('/privacy-policy', [PageController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/terms-of-service', [PageController::class, 'termsOfService'])->name('terms-of-service');
Route::get('/store-theme-demo', [PageController::class, 'storeThemeDemo'])->name('store-theme-demo');

