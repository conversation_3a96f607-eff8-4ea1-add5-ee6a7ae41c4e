/* Vendor Dashboard Styles - Consistent with Homepage Theme */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
    --whamart-primary: #7ED957;
    --whamart-primary-dark: #5DC264;
    --whamart-secondary: #4CAF50;
    --whamart-secondary-dark: #3EA045;
    --whamart-light-bg: #F8FFF5;
    --whamart-hover-bg: #E8FFE1;
    --whamart-text-dark: #333;
    --whamart-text-light: #666;
    --whamart-text-muted: #999;
    --whamart-border: #E5E7EB;
    --whamart-white: #FFFFFF;
    --whamart-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --whamart-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --whamart-radius: 12px;
    --whamart-radius-lg: 16px;
    --whamart-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dashboard Layout */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background: linear-gradient(120deg, #f7f8fa 0%, #e9ffe8 100%);
    font-family: 'Poppins', sans-serif;
}

/* Sidebar Styles */
.vendor-sidebar {
    width: 280px;
    background: var(--whamart-white);
    border-right: 1px solid var(--whamart-border);
    display: flex;
    flex-direction: column;
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 40;
    box-shadow: var(--whamart-shadow);
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid var(--whamart-border);
    display: flex;
    align-items: center;
    gap: 12px;
}

.sidebar-logo {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.sidebar-logo-img {
    width: 28px;
    height: 28px;
    object-fit: contain;
}

.sidebar-logo-fallback {
    color: white;
    font-weight: 700;
    font-size: 18px;
}

.sidebar-brand {
    flex: 1;
}

.sidebar-brand-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--whamart-text-dark);
    margin: 0;
    line-height: 1.2;
}

.sidebar-brand-subtitle {
    font-size: 12px;
    color: var(--whamart-text-muted);
    margin: 0;
    line-height: 1.2;
}

/* Navigation Styles */
.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 32px;
}

.nav-section-title {
    font-size: 11px;
    font-weight: 600;
    color: var(--whamart-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 20px 12px;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--whamart-text-light);
    text-decoration: none;
    transition: var(--whamart-transition);
    position: relative;
    border-radius: 0;
}

.nav-link:hover {
    background: var(--whamart-hover-bg);
    color: var(--whamart-text-dark);
}

.nav-link.active {
    background: var(--whamart-light-bg);
    color: var(--whamart-primary-dark);
    font-weight: 600;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--whamart-primary);
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 16px;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
}

.nav-indicator {
    margin-left: auto;
    width: 6px;
    height: 6px;
    background: var(--whamart-primary);
    border-radius: 50%;
}

/* User Profile in Sidebar */
.sidebar-user {
    padding: 20px;
    border-top: 1px solid var(--whamart-border);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-avatar-fallback {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 16px;
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--whamart-text-dark);
    margin: 0;
    line-height: 1.2;
}

.user-role {
    font-size: 12px;
    color: var(--whamart-text-muted);
    margin: 0;
    line-height: 1.2;
}

.user-actions {
    flex-shrink: 0;
}

.logout-form {
    margin: 0;
}

.logout-btn {
    background: none;
    border: none;
    color: var(--whamart-text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--whamart-transition);
}

.logout-btn:hover {
    background: var(--whamart-hover-bg);
    color: var(--whamart-text-dark);
}

/* Mobile Sidebar */
.mobile-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: var(--whamart-white);
    z-index: 50;
    box-shadow: var(--whamart-shadow-lg);
}

.sidebar-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
}

.mobile-close-btn {
    background: none;
    border: none;
    color: var(--whamart-text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    margin-left: auto;
}

/* Header Styles */
.vendor-header {
    background: var(--whamart-white);
    border-bottom: 1px solid var(--whamart-border);
    padding: 16px 24px;
    position: sticky;
    top: 0;
    z-index: 30;
    box-shadow: var(--whamart-shadow);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-menu-toggle,
.sidebar-toggle {
    background: none;
    border: none;
    color: var(--whamart-text-light);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--whamart-transition);
}

.mobile-menu-toggle:hover,
.sidebar-toggle:hover {
    background: var(--whamart-hover-bg);
    color: var(--whamart-text-dark);
}

.header-title {
    flex: 1;
}

.page-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--whamart-text-dark);
    margin: 0;
    line-height: 1.2;
}

.page-subtitle {
    font-size: 14px;
    color: var(--whamart-text-muted);
    margin: 0;
    line-height: 1.2;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-action-item {
    position: relative;
}

.action-btn {
    background: none;
    border: none;
    color: var(--whamart-text-light);
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: var(--whamart-transition);
    position: relative;
}

.action-btn:hover {
    background: var(--whamart-hover-bg);
    color: var(--whamart-text-dark);
}

.notification-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: #EF4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.content-area {
    flex: 1;
    padding: 24px;
}

/* User Profile Button */
.user-profile-btn {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: var(--whamart-transition);
}

.user-profile-btn:hover {
    background: var(--whamart-hover-bg);
}

.user-dropdown-icon {
    font-size: 12px;
    color: var(--whamart-text-muted);
    transition: var(--whamart-transition);
}

/* Dropdowns */
.notifications-dropdown,
.quick-actions-dropdown,
.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--whamart-white);
    border: 1px solid var(--whamart-border);
    border-radius: var(--whamart-radius);
    box-shadow: var(--whamart-shadow-lg);
    min-width: 320px;
    z-index: 50;
    overflow: hidden;
}

.dropdown-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--whamart-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dropdown-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--whamart-text-dark);
    margin: 0;
}

.notification-count {
    font-size: 12px;
    color: var(--whamart-text-muted);
    background: var(--whamart-light-bg);
    padding: 4px 8px;
    border-radius: 12px;
}

/* Notification Items */
.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
    border-bottom: 1px solid var(--whamart-border);
    transition: var(--whamart-transition);
}

.notification-item:hover {
    background: var(--whamart-light-bg);
}

.notification-item.unread {
    background: rgba(126, 217, 87, 0.05);
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--whamart-light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--whamart-primary);
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--whamart-text-dark);
    margin: 0 0 4px 0;
}

.notification-text {
    font-size: 13px;
    color: var(--whamart-text-light);
    margin: 0 0 4px 0;
}

.notification-time {
    font-size: 12px;
    color: var(--whamart-text-muted);
}

/* Quick Actions */
.quick-actions-list {
    padding: 8px 0;
}

.quick-action-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--whamart-text-dark);
    text-decoration: none;
    transition: var(--whamart-transition);
}

.quick-action-item:hover {
    background: var(--whamart-light-bg);
}

.action-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: var(--whamart-light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--whamart-primary);
}

.action-content {
    flex: 1;
}

.action-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--whamart-text-dark);
    margin: 0;
}

.action-description {
    font-size: 12px;
    color: var(--whamart-text-muted);
    margin: 0;
}

/* User Dropdown */
.user-dropdown {
    min-width: 240px;
}

.user-dropdown .dropdown-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

.user-dropdown .user-avatar {
    width: 48px;
    height: 48px;
}

.user-dropdown .user-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--whamart-text-dark);
    margin: 0;
}

.user-email {
    font-size: 13px;
    color: var(--whamart-text-muted);
    margin: 0;
}

.dropdown-menu {
    padding: 8px 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--whamart-text-dark);
    text-decoration: none;
    transition: var(--whamart-transition);
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
}

.dropdown-item:hover {
    background: var(--whamart-light-bg);
}

.dropdown-item i {
    width: 16px;
    color: var(--whamart-text-muted);
}

.dropdown-divider {
    height: 1px;
    background: var(--whamart-border);
    margin: 8px 0;
}

.logout-item {
    color: #EF4444;
}

.logout-item:hover {
    background: rgba(239, 68, 68, 0.1);
}

.dropdown-footer {
    padding: 12px 20px;
    border-top: 1px solid var(--whamart-border);
}

.view-all-btn {
    display: block;
    text-align: center;
    color: var(--whamart-primary);
    text-decoration: none;
    font-size: 13px;
    font-weight: 600;
    transition: var(--whamart-transition);
}

.view-all-btn:hover {
    color: var(--whamart-primary-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .vendor-sidebar {
        transform: translateX(-100%);
    }

    .main-content {
        margin-left: 0;
    }

    .header-title .page-subtitle {
        display: none;
    }

    .user-info {
        display: none;
    }

    .notifications-dropdown,
    .quick-actions-dropdown,
    .user-dropdown {
        min-width: 280px;
        right: -20px;
    }
}

/* Dashboard Content Styles */
.vendor-dashboard {
    padding: 0;
    background: transparent;
}

.welcome-section {
    background: linear-gradient(135deg, var(--whamart-white) 0%, var(--whamart-light-bg) 100%);
    border-radius: var(--whamart-radius-lg);
    padding: 32px;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--whamart-shadow);
    border-left: 4px solid var(--whamart-primary);
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(126, 217, 87, 0.1) 0%, transparent 70%);
    z-index: 0;
}

.welcome-content {
    position: relative;
    z-index: 1;
}

.welcome-badge {
    display: inline-block;
    padding: 8px 16px;
    background: var(--whamart-light-bg);
    color: var(--whamart-primary-dark);
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 16px;
    border: 1px solid rgba(126, 217, 87, 0.2);
}

.welcome-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--whamart-text-dark);
    margin-bottom: 8px;
    line-height: 1.2;
}

.welcome-subtitle {
    font-size: 16px;
    color: var(--whamart-text-light);
    margin: 0;
    max-width: 600px;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--whamart-white);
    border-radius: var(--whamart-radius);
    padding: 24px;
    box-shadow: var(--whamart-shadow);
    transition: var(--whamart-transition);
    border: 1px solid var(--whamart-border);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--whamart-shadow-lg);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--whamart-primary), var(--whamart-secondary));
}

.stat-card-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-icon-container {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    flex-shrink: 0;
}

.stat-details {
    flex: 1;
}

.stat-label {
    font-size: 14px;
    color: var(--whamart-text-muted);
    margin: 0 0 4px 0;
    font-weight: 500;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--whamart-text-dark);
    margin: 0 0 4px 0;
    line-height: 1;
}

.stat-change {
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-change.positive {
    color: #10B981;
}

.stat-change.negative {
    color: #EF4444;
}

/* Content Cards */
.content-card {
    background: var(--whamart-white);
    border-radius: var(--whamart-radius);
    box-shadow: var(--whamart-shadow);
    border: 1px solid var(--whamart-border);
    overflow: hidden;
    margin-bottom: 24px;
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--whamart-border);
    display: flex;
    align-items: center;
    justify-content: between;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--whamart-text-dark);
    margin: 0;
}

.card-subtitle {
    font-size: 14px;
    color: var(--whamart-text-muted);
    margin: 4px 0 0 0;
}

.card-actions {
    margin-left: auto;
}

.card-content {
    padding: 24px;
}

/* Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--whamart-border);
}

.data-table th {
    background: var(--whamart-light-bg);
    font-weight: 600;
    color: var(--whamart-text-dark);
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table td {
    font-size: 14px;
    color: var(--whamart-text-light);
}

.data-table tr:hover {
    background: var(--whamart-light-bg);
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.completed {
    background: rgba(16, 185, 129, 0.1);
    color: #10B981;
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
}

.status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: #EF4444;
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.quick-action-card {
    background: var(--whamart-white);
    border-radius: var(--whamart-radius);
    padding: 20px;
    box-shadow: var(--whamart-shadow);
    border: 1px solid var(--whamart-border);
    text-decoration: none;
    color: inherit;
    transition: var(--whamart-transition);
    display: flex;
    align-items: center;
    gap: 16px;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--whamart-shadow-lg);
    border-color: var(--whamart-primary);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--whamart-text-dark);
    margin: 0 0 4px 0;
}

.quick-action-description {
    font-size: 13px;
    color: var(--whamart-text-muted);
    margin: 0;
}
